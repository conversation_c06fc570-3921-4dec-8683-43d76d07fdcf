<script lang="ts">
  import { goto } from '$app/navigation';
  import { page } from '$app/stores';
  import type { LayoutData } from './$types';
  import '../../app.css';

  export let data: LayoutData;
  let sidebarOpen = false;

  $: currentPath = $page.url.pathname;

  $: {
    if (typeof document !== 'undefined') {
      if (sidebarOpen) {
        document.body.classList.add('overflow-hidden');
      } else {
        document.body.classList.remove('overflow-hidden');
      }
    }
  }

  async function handleLogout() {
    try {
      await fetch('/api/auth/logout', {
        method: 'POST',
      });
      goto('/login');
    } catch (error) {
      console.error('Logout error:', error);
    }
  }

  function closeSidebar() {
    sidebarOpen = false;
  }
</script>

<svelte:head>
  <title>Dashboard - Routine Mail</title>
</svelte:head>

<!-- Mobile-first responsive layout -->
<div class="min-h-screen bg-gray-50">
  <!-- Desktop Sidebar -->
  <aside class="hidden lg:fixed lg:inset-y-0 lg:left-0 lg:z-50 lg:block lg:w-64 lg:bg-white lg:border-r lg:border-gray-200">
    <!-- Desktop Sidebar Header -->
    <div class="flex items-center px-6 py-6 border-b border-gray-200">
      <a href="/dashboard/tasks" class="flex items-center gap-3 text-xl font-semibold text-gray-900 hover:text-blue-600 transition-colors">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M4 7.00005L10.2 11.65C11.2667 12.45 12.7333 12.45 13.8 11.65L20 7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          <rect x="3" y="5" width="18" height="14" rx="2" stroke="currentColor" stroke-width="2" stroke-linecap="round"></rect>
        </svg>
        <span>Routine Mail</span>
      </a>
    </div>

    <!-- Desktop Navigation -->
    <nav class="flex-1 px-4 py-6 space-y-2">
      <a href="/dashboard/tasks"
         class="flex items-center gap-3 px-4 py-3 rounded-lg font-medium transition-colors {currentPath.startsWith('/dashboard/tasks') ? 'bg-blue-50 text-blue-600 border-r-2 border-blue-600' : 'text-gray-700 hover:bg-gray-100 hover:text-blue-600'}">
        <svg class="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
          <polyline points="14 2 14 8 20 8"></polyline>
          <line x1="16" y1="13" x2="8" y2="13"></line>
          <line x1="16" y1="17" x2="8" y2="17"></line>
          <polyline points="10 9 9 9 8 9"></polyline>
        </svg>
        <span>Tasks</span>
      </a>

      <a href="/dashboard/categories"
         class="flex items-center gap-3 px-4 py-3 rounded-lg font-medium transition-colors {currentPath.startsWith('/dashboard/categories') ? 'bg-blue-50 text-blue-600 border-r-2 border-blue-600' : 'text-gray-700 hover:bg-gray-100 hover:text-blue-600'}">
        <svg class="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
          <line x1="9" y1="9" x2="15" y2="15"></line>
          <line x1="15" y1="9" x2="9" y2="15"></line>
        </svg>
        <span>Categories</span>
      </a>

      <a href="/dashboard/settings"
         class="flex items-center gap-3 px-4 py-3 rounded-lg font-medium transition-colors {currentPath.startsWith('/dashboard/settings') ? 'bg-blue-50 text-blue-600 border-r-2 border-blue-600' : 'text-gray-700 hover:bg-gray-100 hover:text-blue-600'}">
        <svg class="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 0 2l-.15.08a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l-.22-.38a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1 0-2l.15.08a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"></path>
          <circle cx="12" cy="12" r="3"></circle>
        </svg>
        <span>Settings</span>
      </a>
    </nav>

    <!-- Desktop User Profile -->
    <div class="px-4 py-6 border-t border-gray-200">
      <div class="space-y-3">
        <div class="text-sm font-medium text-gray-500 truncate">
          {data.user.email}
        </div>
        <button
          class="w-full px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
          on:click={handleLogout}>
          Logout
        </button>
      </div>
    </div>
  </aside>

  <!-- Mobile Sidebar Overlay - only show on medium screens (md) where bottom nav is hidden -->
  <aside class="hidden md:block lg:hidden fixed inset-y-0 left-0 z-50 w-80 bg-white border-r border-gray-200 transform transition-transform duration-300 ease-in-out {sidebarOpen ? 'translate-x-0' : '-translate-x-full'}">
    <!-- Mobile Sidebar Header -->
    <div class="flex items-center justify-between px-6 py-6 border-b border-gray-200">
      <a href="/dashboard/tasks" class="flex items-center gap-3 text-xl font-semibold text-gray-900" on:click={closeSidebar}>
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M4 7.00005L10.2 11.65C11.2667 12.45 12.7333 12.45 13.8 11.65L20 7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          <rect x="3" y="5" width="18" height="14" rx="2" stroke="currentColor" stroke-width="2" stroke-linecap="round"></rect>
        </svg>
        <span>Routine Mail</span>
      </a>
      <button class="p-2 -mr-2 text-gray-500 hover:text-gray-700" on:click={closeSidebar}>
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <line x1="18" y1="6" x2="6" y2="18"></line>
          <line x1="6" y1="6" x2="18" y2="18"></line>
        </svg>
      </button>
    </div>

    <!-- Mobile Navigation -->
    <nav class="flex-1 px-6 py-6 space-y-3">
      <a href="/dashboard/tasks"
         class="flex items-center gap-4 px-4 py-4 rounded-xl font-medium transition-all duration-200 {currentPath.startsWith('/dashboard/tasks') ? 'bg-blue-50 text-blue-600 shadow-sm' : 'text-gray-700 hover:bg-gray-50 active:bg-gray-100'}"
         on:click={closeSidebar}>
        <div class="flex items-center justify-center w-10 h-10 rounded-lg {currentPath.startsWith('/dashboard/tasks') ? 'bg-blue-100' : 'bg-gray-100'}">
          <svg class="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
            <polyline points="14 2 14 8 20 8"></polyline>
            <line x1="16" y1="13" x2="8" y2="13"></line>
            <line x1="16" y1="17" x2="8" y2="17"></line>
            <polyline points="10 9 9 9 8 9"></polyline>
          </svg>
        </div>
        <span class="text-lg">Tasks</span>
      </a>

      <a href="/dashboard/categories"
         class="flex items-center gap-4 px-4 py-4 rounded-xl font-medium transition-all duration-200 {currentPath.startsWith('/dashboard/categories') ? 'bg-blue-50 text-blue-600 shadow-sm' : 'text-gray-700 hover:bg-gray-50 active:bg-gray-100'}"
         on:click={closeSidebar}>
        <div class="flex items-center justify-center w-10 h-10 rounded-lg {currentPath.startsWith('/dashboard/categories') ? 'bg-blue-100' : 'bg-gray-100'}">
          <svg class="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
            <line x1="9" y1="9" x2="15" y2="15"></line>
            <line x1="15" y1="9" x2="9" y2="15"></line>
          </svg>
        </div>
        <span class="text-lg">Categories</span>
      </a>

      <a href="/dashboard/settings"
         class="flex items-center gap-4 px-4 py-4 rounded-xl font-medium transition-all duration-200 {currentPath.startsWith('/dashboard/settings') ? 'bg-blue-50 text-blue-600 shadow-sm' : 'text-gray-700 hover:bg-gray-50 active:bg-gray-100'}"
         on:click={closeSidebar}>
        <div class="flex items-center justify-center w-10 h-10 rounded-lg {currentPath.startsWith('/dashboard/settings') ? 'bg-blue-100' : 'bg-gray-100'}">
          <svg class="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 0 2l-.15.08a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l-.22-.38a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1 0-2l.15.08a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"></path>
            <circle cx="12" cy="12" r="3"></circle>
          </svg>
        </div>
        <span class="text-lg">Settings</span>
      </a>
    </nav>

    <!-- Mobile User Profile -->
    <div class="px-6 py-6 border-t border-gray-200">
      <div class="space-y-4">
        <div class="flex items-center gap-3">
          <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
            <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
            </svg>
          </div>
          <div class="flex-1 min-w-0">
            <div class="text-sm font-medium text-gray-900 truncate">
              {data.user.email}
            </div>
            <div class="text-xs text-gray-500">
              Logged in
            </div>
          </div>
        </div>
        <button
          class="w-full px-4 py-3 text-sm font-medium text-gray-700 bg-gray-100 rounded-xl hover:bg-gray-200 transition-colors"
          on:click={handleLogout}>
          <div class="flex items-center justify-center gap-2">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
            </svg>
            <span>Logout</span>
          </div>
        </button>
      </div>
    </div>
  </aside>

  <!-- Mobile Header -->
  <header class="lg:hidden flex items-center justify-between px-4 py-4 bg-white border-b border-gray-200 sticky top-0 z-30">
    <a href="/dashboard/tasks" class="flex items-center gap-2 text-lg font-semibold text-gray-900">
      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M4 7.00005L10.2 11.65C11.2667 12.45 12.7333 12.45 13.8 11.65L20 7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
        <rect x="3" y="5" width="18" height="14" rx="2" stroke="currentColor" stroke-width="2" stroke-linecap="round"></rect>
      </svg>
      <span>Routine Mail</span>
    </a>
    <!-- Sidebar toggle button - only show on medium screens (md) where bottom nav is hidden -->
    <button
      class="hidden md:block lg:hidden p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
      on:click={() => sidebarOpen = !sidebarOpen}>
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <line x1="3" y1="12" x2="21" y2="12"></line>
        <line x1="3" y1="6" x2="21" y2="6"></line>
        <line x1="3" y1="18" x2="21" y2="18"></line>
      </svg>
    </button>
  </header>

  <!-- Main Content -->
  <div class="lg:ml-64">
    <main class="px-4 py-6 pb-6 md:pb-20 lg:px-8 lg:py-10 lg:pb-10 max-w-7xl mx-auto">
      <slot />
    </main>
  </div>

  <!-- Mobile Bottom Navigation - only show on small screens (below md) -->
  <nav class="md:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-30">
    <div class="flex items-center justify-around py-2">
      <a href="/dashboard/tasks"
         class="flex flex-col items-center justify-center px-3 py-2 text-xs font-medium transition-colors {currentPath.startsWith('/dashboard/tasks') ? 'text-blue-600' : 'text-gray-600 hover:text-blue-600'}">
        <svg class="w-6 h-6 mb-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
          <polyline points="14 2 14 8 20 8"></polyline>
          <line x1="16" y1="13" x2="8" y2="13"></line>
          <line x1="16" y1="17" x2="8" y2="17"></line>
          <polyline points="10 9 9 9 8 9"></polyline>
        </svg>
        <span>Tasks</span>
      </a>

      <a href="/dashboard/categories"
         class="flex flex-col items-center justify-center px-3 py-2 text-xs font-medium transition-colors {currentPath.startsWith('/dashboard/categories') ? 'text-blue-600' : 'text-gray-600 hover:text-blue-600'}">
        <svg class="w-6 h-6 mb-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
          <line x1="9" y1="9" x2="15" y2="15"></line>
          <line x1="15" y1="9" x2="9" y2="15"></line>
        </svg>
        <span>Categories</span>
      </a>

      <a href="/dashboard/settings"
         class="flex flex-col items-center justify-center px-3 py-2 text-xs font-medium transition-colors {currentPath.startsWith('/dashboard/settings') ? 'text-blue-600' : 'text-gray-600 hover:text-blue-600'}">
        <svg class="w-6 h-6 mb-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 0 2l-.15.08a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l-.22-.38a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1 0-2l.15.08a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"></path>
          <circle cx="12" cy="12" r="3"></circle>
        </svg>
        <span>Settings</span>
      </a>

      <button on:click={handleLogout}
              class="flex flex-col items-center justify-center px-3 py-2 text-xs font-medium text-gray-600 hover:text-blue-600 transition-colors">
        <svg class="w-6 h-6 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
        </svg>
        <span>Logout</span>
      </button>
    </div>
  </nav>

  <!-- Mobile Sidebar Overlay Background - only show on medium screens -->
  {#if sidebarOpen}
    <div class="hidden md:block lg:hidden fixed inset-0 z-40 bg-black bg-opacity-50" on:click={closeSidebar}></div>
  {/if}
</div>
